/**
 * Flaha PA Brand Colors
 *
 * @format
 */

:root {
	--flaha-green: #3d7a42;
	--tech-blue: #1e5f8c;
	--pa-accent: #2196f3;
	--earth-brown: #8c6d4f;
	--neutral-gray: #bcbec0;
	--light-gray: #f5f5f5;
	--white: #ffffff;
	--text-dark: #2c3e50;
}

body {
	font-family: "Montserrat", "Open Sans", "Helvetica Neue", Helvetica, Arial,
		sans-serif;
	margin: 0;
	padding: 0;
	background-color: var(--light-gray);
	color: var(--text-dark);
	line-height: 1.6;
}

/* Flaha PA Header Styles */
.flaha-header {
	background: linear-gradient(
		135deg,
		var(--flaha-green) 0%,
		var(--tech-blue) 100%
	);
	color: var(--white);
	padding: 20px 0;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	margin-bottom: 30px;
}

.header-content {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
}

.brand-section {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

.flaha-logo {
	display: flex;
	align-items: baseline;
	margin-bottom: 5px;
}

.flaha-text {
	font-family: "Montserrat", sans-serif;
	font-weight: 700;
	font-size: 28px;
	color: var(--white);
	margin-right: 8px;
}

.pa-text {
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	font-size: 20px;
	color: var(--pa-accent);
	background-color: var(--white);
	padding: 2px 8px;
	border-radius: 4px;
}

.brand-tagline {
	font-family: "Open Sans", sans-serif;
	font-size: 14px;
	font-weight: 400;
	color: rgba(255, 255, 255, 0.9);
	margin-left: 2px;
}

.app-title-section {
	text-align: right;
}

.app-title {
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	font-size: 32px;
	color: var(--white);
	margin: 0;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-subtitle {
	font-family: "Open Sans", sans-serif;
	font-size: 16px;
	font-weight: 400;
	color: rgba(255, 255, 255, 0.9);
	margin: 5px 0 0 0;
}

/* Responsive header */
@media (max-width: 768px) {
	.header-content {
		flex-direction: column;
		text-align: center;
	}

	.app-title-section {
		text-align: center;
		margin-top: 15px;
	}

	.app-title {
		font-size: 28px;
	}
}

/* Main Content Area */
.main-content {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

#chart-container {
	max-width: 800px;
	margin: 0 auto;
	background-color: var(--white);
	border-radius: 12px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	padding: 20px;
	margin-bottom: 30px;
}

#coordinates {
	text-align: center;
	margin-top: 20px;
	font-size: 14px;
	font-weight: 500;
	color: var(--tech-blue);
}

.ternary-circle {
	stroke: #c00;
	fill: #fff;
	stroke-width: 2px;
}

.ternary-line {
	stroke: #333;
	stroke-width: 1px;
	transition: all 0.2s ease;
}

.ternary-tick {
	fill: none;
	stroke: #aaa;
	stroke-width: 1px;
}

.minor {
	stroke: #ddd;
	stroke-width: 0.5px;
}

text {
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
	font-weight: 300;
}

.tick-label {
	font-size: 10px;
}

.axis-label {
	font-size: 14px;
	font-weight: 500;
	fill: #333;
}

.neatline {
	stroke: black;
	stroke-width: 2px;
	fill: none;
}

.soil-region {
	stroke: #333;
	stroke-width: 1px;
	fill-opacity: 0.5;
	transition: all 0.2s ease;
}

.soil-region:hover {
	fill-opacity: 0.8;
	stroke-width: 2px;
}

.legend-item text {
	font-size: 12px;
}

.input-container {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 30px;
	flex-wrap: wrap;
	background-color: var(--white);
	padding: 25px;
	border-radius: 12px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	max-width: 800px;
	margin-left: auto;
	margin-right: auto;
}

.input-group {
	margin: 0 15px 10px 15px;
	display: flex;
	align-items: center;
}

.input-group label {
	margin-right: 8px;
	font-weight: 600;
	font-family: "Montserrat", sans-serif;
	color: var(--text-dark);
	font-size: 14px;
}

.disabled-label {
	color: var(--neutral-gray);
}

input[type="number"] {
	width: 70px;
	padding: 8px 12px;
	border: 2px solid var(--neutral-gray);
	border-radius: 6px;
	font-family: "Open Sans", sans-serif;
	font-size: 14px;
	transition: border-color 0.3s ease;
}

input[type="number"]:focus {
	outline: none;
	border-color: var(--pa-accent);
	box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

input[type="number"]:disabled {
	background-color: var(--light-gray);
	color: var(--neutral-gray);
}

button {
	margin-left: 15px;
	padding: 10px 20px;
	background: linear-gradient(
		135deg,
		var(--flaha-green) 0%,
		var(--tech-blue) 100%
	);
	color: var(--white);
	border: none;
	border-radius: 6px;
	cursor: pointer;
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	font-size: 14px;
	transition: all 0.3s ease;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

button:hover {
	transform: translateY(-1px);
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

button:active {
	transform: translateY(0);
}

/* Advanced inputs and results with Flaha PA branding */
.advanced-inputs {
	max-width: 800px;
	margin: 0 auto 30px;
	padding: 25px;
	background-color: var(--white);
	border-radius: 12px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	border-top: 4px solid var(--pa-accent);
}

.advanced-inputs h3 {
	margin-top: 0;
	margin-bottom: 20px;
	color: var(--tech-blue);
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	font-size: 20px;
}

.results-container {
	max-width: 800px;
	margin: 30px auto;
	padding: 25px;
	background-color: var(--white);
	border-radius: 12px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	border-top: 4px solid var(--flaha-green);
}

.results-container h3 {
	margin-top: 0;
	margin-bottom: 20px;
	color: var(--flaha-green);
	text-align: center;
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	font-size: 22px;
}

.results-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 15px;
}

.result-item {
	display: flex;
	align-items: baseline;
	padding: 10px;
	background-color: white;
	border-radius: 4px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.result-label {
	font-weight: bold;
	margin-right: 5px;
}

.result-value {
	font-family: "Open Sans", monospace;
	font-size: 1.1em;
	color: var(--tech-blue);
	font-weight: 600;
}

.result-unit {
	margin-left: 5px;
	color: var(--neutral-gray);
	font-family: "Open Sans", sans-serif;
}

#soil-texture-display {
	text-align: center;
	font-size: 1.4em;
	font-weight: 600;
	margin: 15px 0;
	color: var(--flaha-green);
	font-family: "Montserrat", sans-serif;
	padding: 15px;
	background-color: var(--white);
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	border-left: 4px solid var(--pa-accent);
}

/* Progress bar styles with Flaha PA branding */
.progress-bar-container {
	width: 100%;
	height: 10px;
	background-color: var(--light-gray);
	border-radius: 6px;
	margin-top: 10px;
	overflow: hidden;
	box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-bar {
	height: 100%;
	background: linear-gradient(
		90deg,
		var(--pa-accent) 0%,
		var(--tech-blue) 100%
	);
	width: 0%;
	transition: width 0.4s ease;
	border-radius: 6px;
}

/* Water visualization styles */
.water-visualization {
	margin-top: 30px;
	text-align: center;
}

.visualization-container {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	align-items: flex-start;
	gap: 30px;
	margin: 20px auto;
	max-width: 800px;
}

.water-container {
	position: relative;
	width: 200px;
	height: 300px;
	border: 2px solid #718096;
	border-radius: 8px;
	background-color: #f7fafc;
	overflow: hidden;
}

.water-level {
	position: absolute;
	width: 100%;
	left: 0;
	height: 2px;
	background-color: #000;
	display: flex;
	align-items: center;
	z-index: 10;
}

.water-level span {
	position: absolute;
	left: -110px;
	font-size: 12px;
	font-weight: bold;
	white-space: nowrap;
	background-color: rgba(255, 255, 255, 0.8);
	padding: 2px 5px;
	border-radius: 3px;
}

.saturation-level {
	background-color: #3182ce;
	top: 10%;
}

.field-capacity-level {
	background-color: #38a169;
	top: 40%;
}

.wilting-point-level {
	background-color: #e53e3e;
	top: 70%;
}

/* Water zones */
.water-zone {
	position: absolute;
	width: 100%;
	left: 0;
	display: flex;
	justify-content: center;
	align-items: center;
}

.zone-label {
	font-size: 11px;
	font-weight: bold;
	text-align: center;
	color: #2d3748;
	padding: 0 5px;
	pointer-events: none;
}

.gravitational-zone {
	top: 10%;
	height: 30%;
	background-color: rgba(144, 205, 244, 0.5);
}

.available-zone {
	top: 40%;
	height: 30%;
	background-color: rgba(154, 230, 180, 0.5);
}

.unavailable-zone {
	top: 70%;
	height: 30%;
	background-color: rgba(254, 178, 178, 0.5);
}

/* Water explanation */
.water-explanation {
	flex: 1;
	min-width: 250px;
	max-width: 400px;
	text-align: left;
	background-color: white;
	padding: 15px;
	border-radius: 8px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.water-explanation h5 {
	margin-top: 0;
	color: #2c5282;
	border-bottom: 1px solid #e2e8f0;
	padding-bottom: 5px;
}

.water-explanation ul {
	padding-left: 20px;
	margin-bottom: 15px;
}

.water-explanation li {
	margin-bottom: 8px;
	font-size: 14px;
	line-height: 1.4;
}

.soil-tip {
	background-color: #ebf8ff;
	padding: 10px;
	border-radius: 4px;
	font-size: 14px;
	margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
	.results-grid {
		grid-template-columns: 1fr;
	}

	.water-container {
		width: 150px;
		height: 250px;
	}

	.water-labels {
		right: -120px;
	}
}

/* Responsive adjustments */
@media (max-width: 768px) {
	.water-container {
		width: 150px;
		height: 250px;
	}

	.water-level span {
		left: 5px;
		font-size: 10px;
	}

	.zone-label {
		font-size: 9px;
	}
}

/* Info section styles */
.info-section {
	margin-top: 30px;
	padding: 20px;
	background-color: #ebf8ff;
	border-radius: 8px;
}

.info-section h4 {
	margin-top: 0;
	color: #2c5282;
	text-align: center;
}

.info-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 20px;
	margin-top: 15px;
}

.info-item {
	background-color: white;
	padding: 15px;
	border-radius: 8px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-item h5 {
	margin-top: 0;
	color: #2c5282;
}

.info-item p {
	margin-bottom: 0;
	font-size: 14px;
	line-height: 1.5;
	color: #4a5568;
}

/* Recommendations styles with Flaha PA branding */
.recommendations-container {
	max-width: 800px;
	margin: 30px auto;
	padding: 25px;
	background-color: var(--white);
	border-radius: 12px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	border-top: 4px solid var(--earth-brown);
}

.recommendations-container h3 {
	margin-top: 0;
	margin-bottom: 20px;
	color: var(--earth-brown);
	text-align: center;
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	font-size: 22px;
}

.recommendations-content {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 15px;
}

.recommendation-section {
	background-color: white;
	padding: 15px;
	border-radius: 4px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.recommendation-section h4 {
	margin-top: 0;
	margin-bottom: 12px;
	color: var(--tech-blue);
	border-bottom: 2px solid var(--pa-accent);
	padding-bottom: 8px;
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	font-size: 16px;
}

.recommendation-section ul {
	margin: 0;
	padding-left: 20px;
}

.recommendation-section li {
	margin-bottom: 5px;
}

/* Footer Styles */
.flaha-footer {
	background: linear-gradient(
		135deg,
		var(--tech-blue) 0%,
		var(--flaha-green) 100%
	);
	color: var(--white);
	padding: 40px 0 20px;
	margin-top: 50px;
}

.footer-content {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 40px;
	margin-bottom: 30px;
}

.footer-section h4 {
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	margin-bottom: 20px;
	color: var(--white);
	font-size: 16px;
}

.footer-link {
	display: block;
	color: rgba(255, 255, 255, 0.8);
	text-decoration: none;
	margin-bottom: 10px;
	font-family: "Open Sans", sans-serif;
	font-size: 14px;
	transition: color 0.3s ease;
}

.footer-link:hover {
	color: var(--white);
	text-decoration: underline;
}

.footer-brand {
	margin-bottom: 15px;
}

.footer-description {
	color: rgba(255, 255, 255, 0.8);
	font-family: "Open Sans", sans-serif;
	font-size: 14px;
	line-height: 1.6;
	margin: 0;
}

.footer-bottom {
	border-top: 1px solid rgba(255, 255, 255, 0.2);
	padding-top: 20px;
	text-align: center;
}

.footer-bottom p {
	margin: 5px 0;
	font-family: "Open Sans", sans-serif;
	font-size: 14px;
	color: rgba(255, 255, 255, 0.7);
}

/* Enhanced Footer Responsive Design */
@media (max-width: 1024px) {
	.footer-content {
		grid-template-columns: repeat(2, 1fr);
		gap: 30px;
	}
}

@media (max-width: 768px) {
	.flaha-footer {
		padding: 30px 0 15px;
	}

	.footer-content {
		grid-template-columns: 1fr;
		gap: 25px;
		text-align: center;
	}

	.footer-section {
		margin-bottom: 15px;
	}

	.footer-section h4 {
		margin-bottom: 15px;
		font-size: 15px;
	}

	.footer-link {
		font-size: 13px;
		margin-bottom: 8px;
	}

	.footer-description {
		font-size: 13px;
		margin-bottom: 15px;
	}

	.footer-bottom {
		padding-top: 15px;
	}

	.footer-bottom p {
		font-size: 12px;
		margin: 3px 0;
	}
}

/* Responsive adjustments */
@media (max-width: 768px) {
	.results-grid {
		grid-template-columns: 1fr;
	}

	.water-container {
		width: 150px;
		height: 250px;
	}

	.water-labels {
		right: -120px;
	}

	.input-container {
		padding: 20px 15px;
		margin-bottom: 20px;
	}

	.input-group {
		margin: 0 10px 15px 10px;
		flex-direction: column;
		align-items: flex-start;
	}

	.input-group label {
		margin-bottom: 5px;
		margin-right: 0;
	}

	button {
		margin-left: 0;
		margin-top: 15px;
		width: 100%;
	}

	/* Footer responsive */
	.footer-content {
		grid-template-columns: 1fr;
		gap: 30px;
		text-align: center;
	}

	.footer-section {
		margin-bottom: 20px;
	}
}

/* API Integration Styles */
.usage-info {
	background-color: #e3f2fd;
	border: 1px solid #2196f3;
	border-radius: 8px;
	padding: 15px;
	margin-bottom: 20px;
	text-align: center;
}

.usage-message {
	color: var(--tech-blue);
	font-weight: 500;
	font-size: 14px;
}

.usage-message a {
	color: var(--pa-accent);
	text-decoration: none;
	font-weight: 600;
	margin-left: 10px;
}

.usage-message a:hover {
	text-decoration: underline;
}

.error-info {
	background-color: #ffebee;
	border: 1px solid #f44336;
	border-radius: 8px;
	padding: 15px;
	margin-bottom: 20px;
	text-align: center;
}

.error-message {
	color: #d32f2f;
	font-weight: 500;
	font-size: 14px;
}

/* Modal Styles */
.upgrade-modal,
.signup-modal,
.login-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
}

.modal-content {
	background-color: var(--white);
	padding: 30px;
	border-radius: 12px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	max-width: 400px;
	width: 90%;
	text-align: center;
}

.modal-content h3 {
	color: var(--tech-blue);
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	margin-bottom: 20px;
}

.modal-content p {
	color: var(--text-dark);
	margin-bottom: 20px;
	line-height: 1.5;
}

.modal-content form {
	display: flex;
	flex-direction: column;
	gap: 15px;
	margin-bottom: 20px;
}

.modal-content input {
	padding: 12px;
	border: 2px solid var(--neutral-gray);
	border-radius: 6px;
	font-family: "Open Sans", sans-serif;
	font-size: 14px;
	width: 100%;
	box-sizing: border-box;
}

.modal-content input:focus {
	outline: none;
	border-color: var(--pa-accent);
	box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.modal-buttons {
	display: flex;
	gap: 10px;
	justify-content: center;
	flex-wrap: wrap;
}

.modal-content button {
	margin: 5px;
	padding: 12px 20px;
	background: linear-gradient(
		135deg,
		var(--flaha-green) 0%,
		var(--tech-blue) 100%
	);
	color: var(--white);
	border: none;
	border-radius: 6px;
	cursor: pointer;
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	font-size: 14px;
	transition: all 0.3s ease;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modal-content button:hover {
	transform: translateY(-1px);
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.modal-content a {
	color: var(--pa-accent);
	text-decoration: none;
	font-size: 14px;
}

.modal-content a:hover {
	text-decoration: underline;
}

/* Responsive modals */
@media (max-width: 480px) {
	.modal-content {
		padding: 20px;
		margin: 20px;
	}

	.modal-buttons {
		flex-direction: column;
	}

	.modal-content button {
		width: 100%;
	}
}

/* Enhanced Header Navigation */
.header-actions {
	display: flex;
	align-items: center;
	gap: 20px;
	flex-wrap: wrap;
}

.nav-links {
	display: flex;
	gap: 15px;
	align-items: center;
}

.nav-link {
	color: rgba(255, 255, 255, 0.9);
	text-decoration: none;
	font-weight: 500;
	padding: 10px 16px;
	border-radius: 8px;
	transition: all 0.3s ease;
	font-size: 14px;
	white-space: nowrap;
	border: 1px solid transparent;
}

.nav-link:hover {
	background: rgba(255, 255, 255, 0.15);
	color: var(--white);
	border-color: rgba(255, 255, 255, 0.3);
	transform: translateY(-1px);
}

.nav-link.active {
	background: rgba(255, 255, 255, 0.2);
	color: var(--white);
	border-color: rgba(255, 255, 255, 0.4);
	font-weight: 600;
}

.auth-section {
	display: flex;
	gap: 10px;
}

.btn-login {
	background: transparent;
	border: 2px solid var(--pa-accent);
	color: var(--pa-accent);
	padding: 8px 16px;
	border-radius: 6px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-login:hover {
	background: var(--pa-accent);
	color: var(--white);
}

.btn-signup {
	background: var(--pa-accent);
	border: none;
	color: var(--white);
	padding: 10px 20px;
	border-radius: 6px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-signup:hover {
	background: var(--tech-blue);
	transform: translateY(-1px);
}

.user-section {
	position: relative;
	display: flex;
	align-items: center;
	gap: 15px;
}

.plan-status-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 5px;
}

.plan-badge {
	background: rgba(255, 255, 255, 0.2);
	color: var(--white);
	padding: 4px 12px;
	border-radius: 20px;
	font-size: 11px;
	font-weight: 700;
	text-transform: uppercase;
	letter-spacing: 0.5px;
	border: 1px solid rgba(255, 255, 255, 0.3);
}

.usage-counter {
	font-size: 11px;
	color: rgba(255, 255, 255, 0.8);
	text-align: center;
}

.user-menu {
	position: relative;
}

.btn-user {
	background: rgba(255, 255, 255, 0.15);
	color: var(--white);
	border: 1px solid rgba(255, 255, 255, 0.3);
	padding: 12px 18px;
	border-radius: 8px;
	font-weight: 600;
	cursor: pointer;
	display: flex;
	align-items: center;
	gap: 10px;
	transition: all 0.3s ease;
	font-size: 14px;
	backdrop-filter: blur(10px);
}

.btn-user:hover {
	background: rgba(255, 255, 255, 0.25);
	border-color: rgba(255, 255, 255, 0.5);
	transform: translateY(-1px);
}

.dropdown-arrow {
	font-size: 0.8rem;
	transition: transform 0.3s ease;
}

.user-dropdown {
	position: absolute;
	top: calc(100% + 10px);
	right: 0;
	background: var(--white);
	border-radius: 12px;
	box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
	min-width: 200px;
	opacity: 0;
	visibility: hidden;
	transform: translateY(-10px);
	transition: all 0.3s ease;
	z-index: 1000;
	border: 1px solid rgba(0, 0, 0, 0.1);
	overflow: hidden;
}

.user-dropdown.show {
	opacity: 1;
	visibility: visible;
	transform: translateY(0);
}

.user-dropdown::before {
	content: "";
	position: absolute;
	top: -6px;
	right: 20px;
	width: 12px;
	height: 12px;
	background: var(--white);
	border: 1px solid rgba(0, 0, 0, 0.1);
	border-bottom: none;
	border-right: none;
	transform: rotate(45deg);
}

.dropdown-item {
	display: block;
	padding: 14px 20px;
	color: var(--text-dark);
	text-decoration: none;
	transition: all 0.3s ease;
	font-size: 14px;
	font-weight: 500;
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dropdown-item:last-child {
	border-bottom: none;
}

.dropdown-item:hover {
	background: var(--light-gray);
	color: var(--pa-accent);
	padding-left: 24px;
}

.dropdown-item:first-child {
	border-radius: 12px 12px 0 0;
}

.dropdown-item:last-child {
	border-radius: 0 0 12px 12px;
}

.logo-link {
	text-decoration: none;
	color: inherit;
}

/* Mobile Navigation Toggle */
.mobile-nav-toggle {
	display: none;
	background: none;
	border: none;
	color: var(--white);
	font-size: 24px;
	cursor: pointer;
	padding: 8px;
	border-radius: 6px;
	transition: background 0.3s ease;
}

.mobile-nav-toggle:hover {
	background: rgba(255, 255, 255, 0.1);
}

.mobile-nav-toggle span {
	display: block;
	width: 25px;
	height: 3px;
	background: var(--white);
	margin: 5px 0;
	transition: 0.3s;
	border-radius: 2px;
}

/* Responsive header */
@media (max-width: 768px) {
	.header-content {
		flex-wrap: nowrap;
		justify-content: space-between;
	}

	.mobile-nav-toggle {
		display: block;
	}

	.header-actions {
		position: absolute;
		top: 100%;
		left: 0;
		right: 0;
		background: linear-gradient(
			135deg,
			var(--flaha-green) 0%,
			var(--tech-blue) 100%
		);
		flex-direction: column;
		gap: 0;
		padding: 20px;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		transform: translateY(-100%);
		opacity: 0;
		visibility: hidden;
		transition: all 0.3s ease;
		z-index: 999;
	}

	.header-actions.mobile-open {
		transform: translateY(0);
		opacity: 1;
		visibility: visible;
	}

	.nav-links {
		flex-direction: column;
		gap: 0;
		width: 100%;
		order: 1;
	}

	.nav-link {
		padding: 15px 20px;
		border-radius: 0;
		border-bottom: 1px solid rgba(255, 255, 255, 0.1);
		text-align: center;
		width: 100%;
	}

	.nav-link:last-child {
		border-bottom: none;
	}

	.user-section {
		order: 2;
		width: 100%;
		justify-content: center;
		margin-top: 20px;
		padding-top: 20px;
		border-top: 1px solid rgba(255, 255, 255, 0.2);
	}

	.plan-status-container {
		flex-direction: row;
		gap: 15px;
	}

	.user-dropdown {
		position: fixed;
		top: auto;
		bottom: 20px;
		left: 20px;
		right: 20px;
		min-width: auto;
	}

	.user-dropdown::before {
		display: none;
	}
}

/* Enhanced Advanced Inputs Styling */
.input-section {
	margin-bottom: 20px;
	padding: 15px;
	background: white;
	border-radius: 6px;
	border: 1px solid #dee2e6;
}

.input-section h4 {
	margin: 0 0 15px 0;
	color: var(--tech-blue);
	font-size: 1.1em;
	display: flex;
	align-items: center;
	gap: 10px;
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
}

.input-section h5 {
	margin: 15px 0 10px 0;
	color: #6c757d;
	font-size: 0.95em;
	font-weight: 600;
}

/* Input rows and groups */
.input-row {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 15px;
	margin-bottom: 15px;
}

.advanced-inputs .input-group {
	display: flex;
	flex-direction: column;
	margin: 0;
}

.advanced-inputs .input-group label {
	margin-bottom: 5px;
	font-weight: 500;
	color: var(--text-dark);
	font-size: 0.9em;
}

.advanced-inputs .input-group input,
.advanced-inputs .input-group select {
	padding: 8px 12px;
	border: 1px solid #ced4da;
	border-radius: 4px;
	font-size: 14px;
	transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	width: 100%;
}

.advanced-inputs .input-group input:focus,
.advanced-inputs .input-group select:focus {
	outline: none;
	border-color: var(--pa-accent);
	box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
}

.input-help {
	font-size: 0.8em;
	color: #6c757d;
	margin-top: 3px;
	font-style: italic;
}

/* Tier badges */
.tier-badge {
	font-size: 0.7em;
	padding: 2px 6px;
	border-radius: 3px;
	font-weight: bold;
	text-transform: uppercase;
}

.tier-badge.professional {
	background: var(--pa-accent);
	color: white;
}

.tier-badge.expert {
	background: #6f42c1;
	color: white;
}

.tier-badge.enterprise {
	background: var(--earth-brown);
	color: white;
}

/* Toggle sections */
.toggle-section {
	background: none;
	border: none;
	color: var(--pa-accent);
	cursor: pointer;
	font-size: 1em;
	padding: 0;
	margin-left: auto;
}

.collapsible-content {
	margin-top: 15px;
}

/* Expert mode toggle */
.expert-mode-toggle {
	display: flex;
	align-items: center;
	gap: 10px;
	margin: 20px 0;
	padding: 15px;
	background: #e9ecef;
	border-radius: 6px;
}

.toggle-switch {
	position: relative;
	display: inline-block;
	width: 50px;
	height: 24px;
	margin-right: 10px;
}

.toggle-switch input {
	opacity: 0;
	width: 0;
	height: 0;
}

.slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	transition: 0.4s;
	border-radius: 24px;
}

.slider:before {
	position: absolute;
	content: "";
	height: 18px;
	width: 18px;
	left: 3px;
	bottom: 3px;
	background-color: white;
	transition: 0.4s;
	border-radius: 50%;
}

input:checked + .slider {
	background-color: var(--pa-accent);
}

input:checked + .slider:before {
	transform: translateX(26px);
}

.expert-help {
	color: #6c757d;
	font-size: 0.9em;
}

/* Parameter groups */
.parameter-group {
	margin-bottom: 20px;
	padding: 15px;
	background: #f8f9fa;
	border-radius: 4px;
	border-left: 4px solid var(--pa-accent);
}

/* Quality Overview Styling */
.quality-overview {
	display: flex;
	align-items: center;
	gap: 30px;
	margin-bottom: 30px;
	padding: 20px;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 8px;
	border: 1px solid #dee2e6;
}

.quality-score {
	display: flex;
	align-items: center;
	justify-content: center;
}

.score-circle {
	width: 120px;
	height: 120px;
	border-radius: 50%;
	background: linear-gradient(
		135deg,
		var(--flaha-green) 0%,
		var(--tech-blue) 100%
	);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: white;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.score-value {
	font-size: 2.5em;
	font-weight: bold;
	font-family: "Montserrat", sans-serif;
}

.score-label {
	font-size: 0.8em;
	text-align: center;
	margin-top: 5px;
}

.quality-indicators {
	flex: 1;
	display: grid;
	grid-template-columns: 1fr;
	gap: 15px;
}

.indicator {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px 15px;
	background: white;
	border-radius: 6px;
	border-left: 4px solid var(--pa-accent);
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.indicator-label {
	font-weight: 600;
	color: var(--text-dark);
}

.indicator-value {
	font-weight: bold;
	color: var(--tech-blue);
}

/* Confidence info styling */
.confidence-info {
	font-size: 0.75em;
	color: #6c757d;
	margin-top: 5px;
	font-style: italic;
}

/* Professional and Enterprise results */
.professional-results,
.enterprise-results {
	margin-top: 30px;
	padding: 20px;
	background: #f8f9fa;
	border-radius: 8px;
	border-left: 4px solid var(--pa-accent);
}

.professional-results h4,
.enterprise-results h4 {
	margin-top: 0;
	color: var(--tech-blue);
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	display: flex;
	align-items: center;
	gap: 10px;
}

/* Connection Status Styling */
.connection-status {
	margin-bottom: 20px;
	padding: 15px;
	background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
	border: 1px solid #ffeaa7;
	border-radius: 8px;
	border-left: 4px solid #f39c12;
}

.status-message {
	display: flex;
	align-items: center;
	gap: 15px;
	flex-wrap: wrap;
}

.status-icon {
	font-size: 1.5em;
}

.status-text {
	flex: 1;
	color: #856404;
	font-weight: 500;
}

.retry-button {
	background: var(--pa-accent);
	color: white;
	border: none;
	padding: 8px 16px;
	border-radius: 4px;
	cursor: pointer;
	font-weight: 500;
	transition: background-color 0.3s ease;
}

.retry-button:hover {
	background: #1976d2;
}

/* Authentication Prompt Modal */
.auth-prompt-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 2000;
}

.auth-prompt-modal .modal-content {
	background: white;
	border-radius: 12px;
	padding: 0;
	max-width: 500px;
	width: 90%;
	max-height: 90vh;
	overflow-y: auto;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
	animation: modalSlideIn 0.3s ease-out;
}

.auth-prompt-modal .modal-header {
	padding: 24px 24px 0 24px;
	border-bottom: 1px solid #eee;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.auth-prompt-modal .modal-header h3 {
	margin: 0;
	color: var(--pa-accent);
	font-size: 1.5em;
}

.auth-prompt-modal .modal-close {
	background: none;
	border: none;
	font-size: 24px;
	cursor: pointer;
	color: #666;
	padding: 0;
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.auth-prompt-modal .modal-body {
	padding: 24px;
}

.auth-prompt-modal .modal-body p {
	margin-bottom: 20px;
	color: #555;
	font-size: 1.1em;
}

.auth-benefits {
	background: #f8f9fa;
	padding: 20px;
	border-radius: 8px;
	margin: 20px 0;
}

.auth-benefits h4 {
	margin: 0 0 15px 0;
	color: var(--pa-accent);
	font-size: 1.1em;
}

.auth-benefits ul {
	margin: 0;
	padding-left: 0;
	list-style: none;
}

.auth-benefits li {
	padding: 8px 0;
	color: #555;
	font-size: 0.95em;
}

.auth-prompt-modal .modal-footer {
	padding: 0 24px 24px 24px;
	display: flex;
	gap: 12px;
	flex-wrap: wrap;
}

.auth-prompt-modal .btn {
	flex: 1;
	min-width: 120px;
	padding: 12px 20px;
	border: none;
	border-radius: 6px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
	text-align: center;
}

.auth-prompt-modal .btn-primary {
	background: var(--pa-accent);
	color: white;
}

.auth-prompt-modal .btn-primary:hover {
	background: #1976d2;
	transform: translateY(-1px);
}

.auth-prompt-modal .btn-secondary {
	background: #6c757d;
	color: white;
}

.auth-prompt-modal .btn-secondary:hover {
	background: #5a6268;
	transform: translateY(-1px);
}

.auth-prompt-modal .btn-outline {
	background: transparent;
	color: #6c757d;
	border: 1px solid #dee2e6;
}

.auth-prompt-modal .btn-outline:hover {
	background: #f8f9fa;
	border-color: #adb5bd;
}

@keyframes modalSlideIn {
	from {
		opacity: 0;
		transform: translateY(-50px) scale(0.9);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

/* Plan-Based UI Styles */

/* Plan badges */
.plan-badge {
	display: inline-block;
	padding: 4px 8px;
	border-radius: 12px;
	font-size: 0.75em;
	font-weight: 600;
	text-transform: uppercase;
	margin-left: 8px;
}

.plan-free {
	background: #e3f2fd;
	color: #1976d2;
}

.plan-professional {
	background: #f3e5f5;
	color: #7b1fa2;
}

.plan-enterprise {
	background: #fff3e0;
	color: #f57c00;
}

/* Usage counter and display */
.usage-counter {
	font-size: 0.85em;
	margin-top: 8px;
	padding: 8px 12px;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 6px;
	backdrop-filter: blur(10px);
}

.usage-display {
	margin-top: 8px;
	font-size: 0.85em;
}

.usage-info {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.usage-bar {
	width: 100%;
	height: 4px;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 2px;
	overflow: hidden;
}

.usage-progress {
	height: 100%;
	background: linear-gradient(90deg, #4caf50, #ff9800, #f44336);
	transition: width 0.3s ease;
}

.usage-warning {
	color: #ff9800;
}

.usage-warning-text {
	font-size: 0.75em;
	color: #ff9800;
	font-weight: 500;
}

/* Plan notification banners */
.plan-notification {
	padding: 12px;
	margin: 0 0 20px 0;
	border-radius: 8px;
	border-left: 4px solid;
	animation: slideIn 0.3s ease;
}

.notification-info {
	background: #e3f2fd;
	border-color: #2196f3;
	color: #1565c0;
}

.notification-warning {
	background: #fff3e0;
	border-color: #ff9800;
	color: #ef6c00;
}

.notification-success {
	background: #e8f5e8;
	border-color: #4caf50;
	color: #2e7d32;
}

.notification-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 12px;
}

.notification-message {
	flex: 1;
}

.notification-action {
	background: transparent;
	border: 1px solid currentColor;
	color: inherit;
	padding: 4px 12px;
	border-radius: 4px;
	cursor: pointer;
	font-size: 0.85em;
	transition: background-color 0.2s ease;
}

.notification-action:hover {
	background: currentColor;
	color: white;
}

.notification-close {
	background: none;
	border: none;
	font-size: 1.2em;
	cursor: pointer;
	color: inherit;
	opacity: 0.7;
	transition: opacity 0.2s ease;
}

.notification-close:hover {
	opacity: 1;
}

/* Upgrade modals */
.upgrade-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
	animation: fadeIn 0.3s ease;
}

.upgrade-content {
	background: white;
	border-radius: 12px;
	max-width: 500px;
	width: 90%;
	max-height: 90vh;
	overflow-y: auto;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.modal-header {
	padding: 24px 24px 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1px solid #eee;
	margin-bottom: 20px;
}

.modal-close {
	background: none;
	border: none;
	font-size: 1.5em;
	cursor: pointer;
	color: #666;
	transition: color 0.2s ease;
}

.modal-close:hover {
	color: #333;
}

.modal-body {
	padding: 0 24px;
}

.upgrade-message {
	font-size: 1.1em;
	margin-bottom: 20px;
	color: #333;
}

.current-plan {
	background: #f5f5f5;
	padding: 8px 12px;
	border-radius: 6px;
	margin-bottom: 20px;
}

.plan-label {
	font-weight: 600;
	color: #666;
}

.upgrade-benefits h4 {
	margin: 0 0 12px 0;
	color: var(--flaha-green);
}

.feature-list {
	list-style: none;
	padding: 0;
	margin: 0 0 20px 0;
}

.feature-list li {
	padding: 8px 0;
	border-bottom: 1px solid #eee;
}

.feature-list li:last-child {
	border-bottom: none;
}

.pricing-info {
	text-align: center;
	margin: 20px 0;
}

.price {
	font-size: 2em;
	font-weight: bold;
	color: var(--flaha-green);
}

.modal-footer {
	padding: 20px 24px 24px;
	display: flex;
	gap: 12px;
	justify-content: flex-end;
	border-top: 1px solid #eee;
	margin-top: 20px;
}

/* Usage limit modal specific styles */
.usage-limit-modal .modal-content {
	max-width: 600px;
}

.limit-icon {
	font-size: 3em;
	text-align: center;
	margin-bottom: 16px;
}

.limit-message {
	font-size: 1.1em;
	text-align: center;
	margin-bottom: 12px;
	color: #333;
}

.reset-info {
	text-align: center;
	color: #666;
	margin-bottom: 24px;
}

.upgrade-options h4 {
	margin-bottom: 16px;
	color: var(--flaha-green);
}

.option-cards {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16px;
	margin-bottom: 20px;
}

.option-card {
	border: 2px solid #eee;
	border-radius: 8px;
	padding: 20px;
	text-align: center;
	transition: border-color 0.2s ease;
}

.option-card.featured {
	border-color: var(--flaha-green);
	background: #f8fff8;
}

.option-card h5 {
	margin: 0 0 8px 0;
	font-size: 1.2em;
	color: #333;
}

.option-price {
	font-size: 1.5em;
	font-weight: bold;
	color: var(--flaha-green);
	margin-bottom: 12px;
}

.option-card ul {
	list-style: none;
	padding: 0;
	margin: 0 0 16px 0;
	font-size: 0.9em;
}

.option-card li {
	padding: 2px 0;
}

/* Feature upgrade prompts */
.feature-upgrade-prompt {
	position: relative;
	margin-top: 16px;
}

.upgrade-overlay {
	background: linear-gradient(
		135deg,
		rgba(61, 122, 66, 0.05),
		rgba(30, 95, 140, 0.05)
	);
	border: 2px dashed rgba(61, 122, 66, 0.3);
	border-radius: 8px;
	padding: 20px;
	text-align: center;
	backdrop-filter: blur(2px);
}

.upgrade-overlay h4 {
	margin: 0 0 8px 0;
	color: var(--flaha-green);
}

.upgrade-overlay p {
	margin: 0 0 16px 0;
	color: #666;
	font-size: 0.9em;
}

.btn-upgrade {
	background: linear-gradient(135deg, var(--flaha-green), var(--tech-blue));
	color: white;
	border: none;
	padding: 10px 20px;
	border-radius: 6px;
	cursor: pointer;
	font-weight: 600;
	transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-upgrade:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(61, 122, 66, 0.3);
}

/* Success toast */
.success-toast {
	position: fixed;
	top: 20px;
	right: 20px;
	background: #4caf50;
	color: white;
	padding: 16px 20px;
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
	z-index: 1001;
	animation: slideInRight 0.3s ease;
}

.toast-content {
	display: flex;
	align-items: center;
	gap: 8px;
}

.toast-icon {
	font-size: 1.2em;
}

/* Button styles */
.btn-primary {
	background: linear-gradient(135deg, var(--flaha-green), var(--tech-blue));
	color: white;
	border: none;
	padding: 12px 24px;
	border-radius: 6px;
	cursor: pointer;
	font-weight: 600;
	transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-primary:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(61, 122, 66, 0.3);
}

.btn-secondary {
	background: #f5f5f5;
	color: #333;
	border: 1px solid #ddd;
	padding: 12px 24px;
	border-radius: 6px;
	cursor: pointer;
	font-weight: 500;
	transition: background-color 0.2s ease;
}

.btn-secondary:hover {
	background: #e9ecef;
}

.btn-outline {
	background: transparent;
	color: var(--flaha-green);
	border: 2px solid var(--flaha-green);
	padding: 10px 20px;
	border-radius: 6px;
	cursor: pointer;
	font-weight: 600;
	transition: background-color 0.2s ease, color 0.2s ease;
}

.btn-outline:hover {
	background: var(--flaha-green);
	color: white;
}

/* Animations */
@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideIn {
	from {
		transform: translateY(-20px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes slideInRight {
	from {
		transform: translateX(100%);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}

/* Responsive design for mobile */
@media (max-width: 768px) {
	.upgrade-content {
		margin: 20px;
		width: auto;
	}

	.option-cards {
		grid-template-columns: 1fr;
	}

	.modal-footer {
		flex-direction: column;
	}

	.modal-footer button {
		width: 100%;
	}

	.usage-counter,
	.usage-display {
		font-size: 0.8em;
	}

	.plan-badge {
		font-size: 0.7em;
		padding: 2px 6px;
	}
}
