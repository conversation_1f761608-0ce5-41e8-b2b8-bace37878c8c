/**
 * FlahaSoil Landing Page Styles
 *
 * @format
 */

:root {
	--primary-color: #2e7d32;
	--secondary-color: #1976d2;
	--accent-color: #ff6b35;
	--text-dark: #2c3e50;
	--text-light: #7f8c8d;
	--background-light: #f8f9fa;
	--white: #ffffff;
	--shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	--shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.15);
}

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: "Open Sans", sans-serif;
	line-height: 1.6;
	color: var(--text-dark);
	overflow-x: hidden;
}

.container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

/* Navigation */
.navbar {
	background: var(--white);
	box-shadow: var(--shadow);
	position: fixed;
	top: 0;
	width: 100%;
	z-index: 1000;
}

.nav-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 70px;
}

.nav-brand {
	display: flex;
	align-items: center;
	gap: 12px;
}

.nav-logo {
	height: 40px;
	width: auto;
}

.brand-text {
	font-family: "Montserrat", sans-serif;
	font-weight: 700;
	font-size: 24px;
	color: var(--primary-color);
}

.nav-menu {
	display: flex;
	align-items: center;
	gap: 30px;
}

.nav-link {
	text-decoration: none;
	color: var(--text-dark);
	font-weight: 500;
	transition: color 0.3s ease;
}

.nav-link:hover {
	color: var(--primary-color);
}

.btn-login {
	background: transparent;
	border: 2px solid var(--primary-color);
	color: var(--primary-color);
	padding: 8px 20px;
	border-radius: 6px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-login:hover {
	background: var(--primary-color);
	color: var(--white);
}

.btn-signup {
	background: var(--primary-color);
	border: none;
	color: var(--white);
	padding: 10px 24px;
	border-radius: 6px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-signup:hover {
	background: #1b5e20;
	transform: translateY(-1px);
}

.nav-toggle {
	display: none;
	flex-direction: column;
	cursor: pointer;
}

.nav-toggle span {
	width: 25px;
	height: 3px;
	background: var(--text-dark);
	margin: 3px 0;
	transition: 0.3s;
}

/* Hero Section */
.hero {
	background: linear-gradient(135deg, #e8f5e8 0%, #f3e5f5 100%);
	padding: 120px 0 80px;
	min-height: 100vh;
	display: flex;
	align-items: center;
}

.hero-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 60px;
	align-items: center;
}

.hero-title {
	font-family: "Montserrat", sans-serif;
	font-size: 3.5rem;
	font-weight: 700;
	line-height: 1.2;
	margin-bottom: 20px;
	color: var(--text-dark);
}

.hero-subtitle {
	font-size: 1.2rem;
	color: var(--text-light);
	margin-bottom: 40px;
	line-height: 1.6;
}

.hero-buttons {
	display: flex;
	gap: 20px;
	margin-bottom: 60px;
}

.btn-primary {
	background: var(--primary-color);
	color: var(--white);
	border: none;
	padding: 15px 30px;
	border-radius: 8px;
	font-size: 1.1rem;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: var(--shadow);
}

.btn-primary:hover {
	background: #1b5e20;
	transform: translateY(-2px);
	box-shadow: var(--shadow-hover);
}

.btn-secondary {
	background: transparent;
	color: var(--text-dark);
	border: 2px solid var(--text-dark);
	padding: 13px 30px;
	border-radius: 8px;
	font-size: 1.1rem;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-secondary:hover {
	background: var(--text-dark);
	color: var(--white);
}

.hero-stats {
	display: flex;
	gap: 40px;
}

.stat {
	text-align: center;
}

.stat-number {
	display: block;
	font-family: "Montserrat", sans-serif;
	font-size: 2rem;
	font-weight: 700;
	color: var(--primary-color);
}

.stat-label {
	font-size: 0.9rem;
	color: var(--text-light);
}

.hero-visual {
	display: flex;
	justify-content: center;
	align-items: center;
}

.soil-triangle-preview {
	position: relative;
	background: var(--white);
	border-radius: 20px;
	padding: 40px;
	box-shadow: var(--shadow-hover);
	transform: rotate(3deg);
	transition: transform 0.3s ease;
}

.soil-triangle-preview:hover {
	transform: rotate(0deg);
}

.triangle-image {
	width: 300px;
	height: auto;
	border-radius: 10px;
}

.preview-overlay {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.preview-point {
	width: 12px;
	height: 12px;
	background: var(--accent-color);
	border-radius: 50%;
	margin: 0 auto 10px;
	animation: pulse 2s infinite;
}

.preview-label {
	background: var(--accent-color);
	color: var(--white);
	padding: 5px 10px;
	border-radius: 15px;
	font-size: 0.8rem;
	font-weight: 600;
	white-space: nowrap;
}

@keyframes pulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.2);
		opacity: 0.7;
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}

/* Features Section */
.features {
	padding: 100px 0;
	background: var(--white);
}

.section-title {
	font-family: "Montserrat", sans-serif;
	font-size: 2.5rem;
	font-weight: 700;
	text-align: center;
	margin-bottom: 60px;
	color: var(--text-dark);
}

.features-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
	gap: 40px;
}

.feature-card {
	background: var(--white);
	padding: 40px 30px;
	border-radius: 15px;
	text-align: center;
	box-shadow: var(--shadow);
	transition: all 0.3s ease;
}

.feature-card:hover {
	transform: translateY(-5px);
	box-shadow: var(--shadow-hover);
}

.feature-icon {
	font-size: 3rem;
	margin-bottom: 20px;
}

.feature-card h3 {
	font-family: "Montserrat", sans-serif;
	font-size: 1.3rem;
	font-weight: 600;
	margin-bottom: 15px;
	color: var(--text-dark);
}

.feature-card p {
	color: var(--text-light);
	line-height: 1.6;
}

/* Pricing Section */
.pricing {
	padding: 100px 0;
	background: var(--background-light);
}

.pricing-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
	gap: 30px;
	max-width: 1000px;
	margin: 0 auto;
}

.pricing-card {
	background: var(--white);
	border-radius: 15px;
	padding: 40px 30px;
	text-align: center;
	box-shadow: var(--shadow);
	position: relative;
	transition: all 0.3s ease;
}

.pricing-card:hover {
	transform: translateY(-5px);
	box-shadow: var(--shadow-hover);
}

.pricing-card.featured {
	border: 3px solid var(--primary-color);
	transform: scale(1.05);
}

.plan-badge {
	position: absolute;
	top: -15px;
	left: 50%;
	transform: translateX(-50%);
	background: var(--primary-color);
	color: var(--white);
	padding: 8px 20px;
	border-radius: 20px;
	font-size: 0.9rem;
	font-weight: 600;
}

.plan-header h3 {
	font-family: "Montserrat", sans-serif;
	font-size: 1.5rem;
	font-weight: 700;
	margin-bottom: 10px;
	color: var(--text-dark);
}

.price {
	font-family: "Montserrat", sans-serif;
	font-size: 3rem;
	font-weight: 700;
	color: var(--primary-color);
	margin-bottom: 30px;
}

.price span {
	font-size: 1rem;
	color: var(--text-light);
}

.plan-features {
	list-style: none;
	margin-bottom: 40px;
}

.plan-features li {
	padding: 8px 0;
	color: var(--text-dark);
}

.btn-plan {
	width: 100%;
	padding: 15px;
	border: 2px solid var(--primary-color);
	background: transparent;
	color: var(--primary-color);
	border-radius: 8px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-plan:hover {
	background: var(--primary-color);
	color: var(--white);
}

.btn-plan.primary {
	background: var(--primary-color);
	color: var(--white);
}

.btn-plan.primary:hover {
	background: #1b5e20;
}

/* About Section */
.about {
	padding: 100px 0;
	background: var(--white);
}

.about-content {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 60px;
	align-items: center;
}

.about-text h2 {
	font-family: "Montserrat", sans-serif;
	font-size: 2.5rem;
	font-weight: 700;
	margin-bottom: 30px;
	color: var(--text-dark);
}

.about-text p {
	color: var(--text-light);
	margin-bottom: 20px;
	line-height: 1.8;
	font-size: 1.1rem;
}

.about-image img {
	width: 100%;
	height: auto;
	border-radius: 15px;
}

/* Footer */
.footer {
	background: var(--text-dark);
	color: var(--white);
	padding: 60px 0 20px;
}

.footer-content {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 40px;
	margin-bottom: 40px;
}

.footer-brand {
	display: flex;
	align-items: center;
	gap: 12px;
	margin-bottom: 20px;
}

.footer-logo {
	height: 30px;
	width: auto;
	filter: brightness(0) invert(1);
}

.footer-section h4 {
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	margin-bottom: 20px;
}

.footer-section a {
	display: block;
	color: #bdc3c7;
	text-decoration: none;
	margin-bottom: 10px;
	transition: color 0.3s ease;
}

.footer-section a:hover {
	color: var(--white);
}

.footer-bottom {
	border-top: 1px solid #34495e;
	padding-top: 20px;
	text-align: center;
	color: #bdc3c7;
}

/* Feature Comparison Table */
.feature-comparison {
	padding: 100px 0;
	background: var(--white);
}

.comparison-table {
	max-width: 1000px;
	margin: 0 auto;
	overflow-x: auto;
	background: var(--white);
	border-radius: 15px;
	box-shadow: var(--shadow-hover);
}

.comparison-table table {
	width: 100%;
	border-collapse: collapse;
	font-size: 0.95rem;
}

.comparison-table th {
	background: linear-gradient(135deg, var(--primary-color) 0%, #1b5e20 100%);
	color: var(--white);
	padding: 20px 15px;
	text-align: center;
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	border: none;
}

.comparison-table th:first-child {
	text-align: left;
	border-radius: 15px 0 0 0;
}

.comparison-table th:last-child {
	border-radius: 0 15px 0 0;
}

.comparison-table th.featured-col {
	background: linear-gradient(135deg, var(--accent-color) 0%, #e55722 100%);
	position: relative;
}

.comparison-table th.featured-col::after {
	content: "★ MOST POPULAR";
	position: absolute;
	top: -10px;
	left: 50%;
	transform: translateX(-50%);
	background: var(--accent-color);
	color: var(--white);
	padding: 5px 15px;
	border-radius: 15px;
	font-size: 0.7rem;
	white-space: nowrap;
}

.comparison-table td {
	padding: 15px;
	text-align: center;
	border-bottom: 1px solid #e8f5e8;
	color: var(--text-dark);
}

.comparison-table td:first-child {
	text-align: left;
	font-weight: 600;
	background: var(--background-light);
}

.comparison-table .featured-col {
	background: linear-gradient(135deg, #fff8f3 0%, #fff5f0 100%);
	font-weight: 600;
	color: var(--accent-color);
}

.comparison-table tr:last-child td:first-child {
	border-radius: 0 0 0 15px;
}

.comparison-table tr:last-child td:last-child {
	border-radius: 0 0 15px 0;
}

/* Testimonials Section */
.testimonials {
	padding: 100px 0;
	background: var(--background-light);
}

.testimonials-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
	gap: 40px;
	max-width: 1200px;
	margin: 0 auto;
}

.testimonial-card {
	background: var(--white);
	border-radius: 20px;
	padding: 40px 30px;
	box-shadow: var(--shadow);
	transition: all 0.3s ease;
	position: relative;
}

.testimonial-card::before {
	content: '"';
	position: absolute;
	top: -10px;
	left: 30px;
	font-size: 4rem;
	color: var(--primary-color);
	font-family: "Montserrat", sans-serif;
	font-weight: 700;
	opacity: 0.3;
}

.testimonial-card:hover {
	transform: translateY(-5px);
	box-shadow: var(--shadow-hover);
}

.testimonial-content {
	margin-bottom: 30px;
}

.testimonial-content p {
	font-size: 1.1rem;
	line-height: 1.7;
	color: var(--text-dark);
	font-style: italic;
	margin: 0;
}

.testimonial-author {
	display: flex;
	align-items: center;
	gap: 15px;
}

.author-avatar {
	width: 60px;
	height: 60px;
	border-radius: 50%;
	object-fit: cover;
	border: 3px solid var(--background-light);
}

.author-info h4 {
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	color: var(--text-dark);
	margin: 0 0 5px 0;
	font-size: 1.1rem;
}

.author-info p {
	color: var(--text-light);
	margin: 0 0 8px 0;
	font-size: 0.9rem;
}

.rating {
	color: #ffd700;
	font-size: 1.1rem;
}

/* Case Studies Section */
.case-studies {
	padding: 100px 0;
	background: var(--white);
}

.case-studies-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
	gap: 40px;
	max-width: 1200px;
	margin: 0 auto;
}

.case-study-card {
	background: var(--white);
	border-radius: 20px;
	overflow: hidden;
	box-shadow: var(--shadow);
	transition: all 0.3s ease;
}

.case-study-card:hover {
	transform: translateY(-5px);
	box-shadow: var(--shadow-hover);
}

.case-study-image {
	height: 200px;
	overflow: hidden;
	position: relative;
}

.case-study-image img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: transform 0.3s ease;
}

.case-study-card:hover .case-study-image img {
	transform: scale(1.05);
}

.case-study-content {
	padding: 30px;
}

.case-study-content h3 {
	font-family: "Montserrat", sans-serif;
	font-size: 1.4rem;
	font-weight: 700;
	color: var(--text-dark);
	margin-bottom: 10px;
}

.case-study-subtitle {
	color: var(--primary-color);
	font-weight: 600;
	font-size: 0.9rem;
	text-transform: uppercase;
	letter-spacing: 1px;
	margin-bottom: 15px;
}

.case-study-content p {
	color: var(--text-light);
	line-height: 1.6;
	margin-bottom: 25px;
}

.case-study-stats {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20px;
	margin-bottom: 25px;
	padding: 20px;
	background: var(--background-light);
	border-radius: 10px;
}

.case-study-stats .stat {
	text-align: center;
}

.case-study-stats .stat-number {
	display: block;
	font-family: "Montserrat", sans-serif;
	font-size: 1.8rem;
	font-weight: 700;
	color: var(--primary-color);
	margin-bottom: 5px;
}

.case-study-stats .stat-label {
	font-size: 0.8rem;
	color: var(--text-light);
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.read-more-btn {
	color: var(--primary-color);
	text-decoration: none;
	font-weight: 600;
	font-size: 0.9rem;
	display: inline-flex;
	align-items: center;
	gap: 5px;
	transition: all 0.3s ease;
}

.read-more-btn:hover {
	color: var(--accent-color);
	gap: 10px;
}

/* Trust Indicators Section */
.trust-indicators {
	padding: 80px 0;
	background: var(--background-light);
}

.trust-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 40px;
	max-width: 1000px;
	margin: 0 auto;
}

.trust-item {
	text-align: center;
	padding: 30px 20px;
	background: var(--white);
	border-radius: 15px;
	box-shadow: var(--shadow);
	transition: all 0.3s ease;
}

.trust-item:hover {
	transform: translateY(-3px);
	box-shadow: var(--shadow-hover);
}

.trust-item img {
	width: 80px;
	height: 80px;
	object-fit: contain;
	margin-bottom: 20px;
	opacity: 0.8;
	transition: opacity 0.3s ease;
}

.trust-item:hover img {
	opacity: 1;
}

.trust-item p {
	font-weight: 600;
	color: var(--text-dark);
	margin: 0;
	font-size: 0.95rem;
}

/* Enhanced CTA Section */
.cta-section {
	padding: 100px 0;
	background: linear-gradient(135deg, var(--primary-color) 0%, #1b5e20 100%);
	color: var(--white);
	text-align: center;
}

.cta-content h2 {
	font-family: "Montserrat", sans-serif;
	font-size: 3rem;
	font-weight: 700;
	margin-bottom: 20px;
	color: var(--white);
}

.cta-content p {
	font-size: 1.2rem;
	margin-bottom: 40px;
	opacity: 0.95;
	max-width: 600px;
	margin-left: auto;
	margin-right: auto;
}

.cta-buttons {
	display: flex;
	gap: 20px;
	justify-content: center;
	margin-bottom: 30px;
	flex-wrap: wrap;
}

.btn-primary.large {
	padding: 18px 40px;
	font-size: 1.2rem;
	border-radius: 10px;
	background: var(--accent-color);
	border: none;
	box-shadow: 0 8px 20px rgba(255, 107, 53, 0.3);
}

.btn-primary.large:hover {
	background: #e55722;
	transform: translateY(-2px);
	box-shadow: 0 12px 30px rgba(255, 107, 53, 0.4);
}

.btn-secondary.large {
	padding: 16px 40px;
	font-size: 1.2rem;
	border-radius: 10px;
	background: transparent;
	border: 2px solid var(--white);
	color: var(--white);
}

.btn-secondary.large:hover {
	background: var(--white);
	color: var(--primary-color);
}

.cta-note {
	font-size: 0.9rem;
	opacity: 0.8;
	margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
	.nav-menu {
		display: none;
	}

	.nav-toggle {
		display: flex;
	}

	.hero-container {
		grid-template-columns: 1fr;
		text-align: center;
	}

	.hero-title {
		font-size: 2.5rem;
	}

	.hero-buttons {
		flex-direction: column;
		align-items: center;
	}

	.hero-stats {
		justify-content: center;
	}

	.about-content {
		grid-template-columns: 1fr;
		text-align: center;
	}

	.pricing-card.featured {
		transform: none;
	}

	.comparison-table {
		margin: 0 20px;
	}

	.comparison-table th.featured-col::after {
		font-size: 0.6rem;
		padding: 3px 10px;
	}

	.testimonials-grid,
	.case-studies-grid {
		grid-template-columns: 1fr;
		gap: 30px;
	}

	.testimonial-card,
	.case-study-card {
		margin: 0 20px;
	}

	.case-study-stats {
		grid-template-columns: 1fr;
		gap: 15px;
	}

	.trust-grid {
		grid-template-columns: repeat(2, 1fr);
		gap: 20px;
	}

	.cta-content h2 {
		font-size: 2.2rem;
	}

	.cta-buttons {
		flex-direction: column;
		align-items: center;
	}

	.btn-primary.large,
	.btn-secondary.large {
		width: 100%;
		max-width: 300px;
	}
}

@media (max-width: 480px) {
	.trust-grid {
		grid-template-columns: 1fr;
	}

	.comparison-table {
		font-size: 0.85rem;
	}

	.comparison-table th,
	.comparison-table td {
		padding: 10px 8px;
	}
}
