/**
 * Advanced Visualization Styles for FlahaSoil
 * Professional+ feature styling
 *
 * @format
 */

/* Moisture-Tension Curve Styles */
.moisture-tension-container {
	background: white;
	border-radius: 12px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	padding: 20px;
	margin: 20px 0;
	position: relative;
	height: 500px;
}

.key-points-info {
	margin-top: 20px;
	padding: 15px;
	background: #f8f9fa;
	border-radius: 8px;
	border-left: 4px solid #3b82f6;
}

.key-points-info h4 {
	margin: 0 0 15px 0;
	color: #1f2937;
	font-size: 16px;
	font-weight: 600;
}

.key-points-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 15px;
}

.key-point {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px;
	background: white;
	border-radius: 6px;
	border: 1px solid #e5e7eb;
}

.point-label {
	font-weight: 500;
	color: #6b7280;
}

.point-value {
	font-weight: 600;
	color: #1f2937;
	font-size: 16px;
}

/* 3D Soil Profile Styles */
.soil-profile-3d {
	background: white;
	border-radius: 12px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	padding: 20px;
	margin: 20px 0;
}

.profile-title {
	margin: 0 0 20px 0;
	color: #1f2937;
	font-size: 18px;
	font-weight: 600;
	text-align: center;
}

.profile-container {
	display: flex;
	flex-direction: column;
	width: 100%;
	max-width: 300px;
	margin: 0 auto 20px auto;
	border: 2px solid #374151;
	border-radius: 8px;
	overflow: hidden;
	position: relative;
}

.horizon-layer {
	position: relative;
	border-bottom: 1px solid rgba(0, 0, 0, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	cursor: pointer;
}

.horizon-layer:hover {
	filter: brightness(1.1);
	transform: scale(1.02);
	z-index: 10;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.horizon-layer:last-child {
	border-bottom: none;
}

.horizon-info {
	background: rgba(0, 0, 0, 0.7);
	color: white;
	padding: 8px 12px;
	border-radius: 4px;
	font-size: 12px;
	line-height: 1.4;
	text-align: center;
	opacity: 0;
	transition: opacity 0.3s ease;
	position: absolute;
	z-index: 5;
}

.horizon-layer:hover .horizon-info {
	opacity: 1;
}

.profile-summary {
	margin-top: 20px;
	padding: 15px;
	background: #f8f9fa;
	border-radius: 8px;
	border-left: 4px solid #10b981;
}

.profile-summary h4 {
	margin: 0 0 15px 0;
	color: #1f2937;
	font-size: 16px;
	font-weight: 600;
}

.summary-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 15px;
}

.summary-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px;
	background: white;
	border-radius: 6px;
	border: 1px solid #e5e7eb;
}

.summary-label {
	font-weight: 500;
	color: #6b7280;
}

.summary-value {
	font-weight: 600;
	color: #1f2937;
	font-size: 16px;
}

/* Comparative Analysis Styles */
.comparative-analysis {
	background: white;
	border-radius: 12px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	padding: 20px;
	margin: 20px 0;
}

.comparison-table {
	margin-top: 20px;
}

.comparison-table h4 {
	margin: 0 0 15px 0;
	color: #1f2937;
	font-size: 16px;
	font-weight: 600;
}

.comparison-data-table {
	width: 100%;
	border-collapse: collapse;
	background: white;
	border-radius: 8px;
	overflow: hidden;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.comparison-data-table th,
.comparison-data-table td {
	padding: 12px;
	text-align: left;
	border-bottom: 1px solid #e5e7eb;
}

.comparison-data-table th {
	background: #f8f9fa;
	font-weight: 600;
	color: #374151;
}

.comparison-data-table tr:hover {
	background: #f8f9fa;
}

.comparison-data-table tr:last-child td {
	border-bottom: none;
}

/* Real-time Adjustment Styles */
.realtime-adjustment {
	background: white;
	border-radius: 12px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	padding: 20px;
	margin: 20px 0;
}

.realtime-adjustment h3 {
	margin: 0 0 20px 0;
	color: #1f2937;
	font-size: 18px;
	font-weight: 600;
	text-align: center;
}

.adjustment-controls {
	margin-bottom: 30px;
}

.parameter-controls {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 20px;
	margin-top: 20px;
}

.control-group {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.control-group label {
	font-weight: 500;
	color: #374151;
	font-size: 14px;
}

.parameter-slider {
	width: 100%;
	height: 6px;
	border-radius: 3px;
	background: #e5e7eb;
	outline: none;
	-webkit-appearance: none;
	cursor: pointer;
}

.parameter-slider::-webkit-slider-thumb {
	-webkit-appearance: none;
	appearance: none;
	width: 20px;
	height: 20px;
	border-radius: 50%;
	background: #3b82f6;
	cursor: pointer;
	border: 2px solid white;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.parameter-slider::-moz-range-thumb {
	width: 20px;
	height: 20px;
	border-radius: 50%;
	background: #3b82f6;
	cursor: pointer;
	border: 2px solid white;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.control-group span {
	font-weight: 600;
	color: #1f2937;
	font-size: 16px;
	text-align: center;
	padding: 5px;
	background: #f3f4f6;
	border-radius: 4px;
	min-width: 60px;
}

.adjustment-results {
	padding: 20px;
	background: #f8f9fa;
	border-radius: 8px;
	border-left: 4px solid #f59e0b;
}

.adjustment-results h4 {
	margin: 0 0 15px 0;
	color: #1f2937;
	font-size: 16px;
	font-weight: 600;
}

.result-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 15px;
}

.result-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px;
	background: white;
	border-radius: 6px;
	border: 1px solid #e5e7eb;
}

.result-label {
	font-weight: 500;
	color: #6b7280;
}

.result-value {
	font-weight: 600;
	color: #1f2937;
	font-size: 16px;
}

/* Upgrade Prompt Styles */
.upgrade-prompt {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 300px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 12px;
	color: white;
	text-align: center;
	margin: 20px 0;
}

.upgrade-content h3 {
	margin: 0 0 15px 0;
	font-size: 24px;
	font-weight: 600;
}

.upgrade-content p {
	margin: 0 0 20px 0;
	font-size: 16px;
	opacity: 0.9;
}

.upgrade-btn {
	background: white;
	color: #667eea;
	border: none;
	padding: 12px 24px;
	border-radius: 6px;
	font-weight: 600;
	font-size: 16px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.upgrade-btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Error Message Styles */
.error-message {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 200px;
	background: #fee2e2;
	border: 1px solid #fecaca;
	border-radius: 8px;
	color: #dc2626;
	font-weight: 500;
	margin: 20px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
	.parameter-controls {
		grid-template-columns: 1fr;
	}

	.key-points-grid,
	.summary-grid,
	.result-grid {
		grid-template-columns: 1fr;
	}

	.profile-container {
		max-width: 250px;
	}

	.horizon-info {
		font-size: 10px;
		padding: 6px 8px;
	}

	.comparison-data-table {
		font-size: 14px;
	}

	.comparison-data-table th,
	.comparison-data-table td {
		padding: 8px;
	}
}

@media (max-width: 480px) {
	.realtime-adjustment,
	.soil-profile-3d,
	.moisture-tension-container,
	.comparative-analysis {
		padding: 15px;
		margin: 15px 0;
	}

	.profile-container {
		max-width: 200px;
	}

	.horizon-info {
		font-size: 9px;
		padding: 4px 6px;
	}

	.upgrade-content h3 {
		font-size: 20px;
	}

	.upgrade-content p {
		font-size: 14px;
	}

	.upgrade-btn {
		padding: 10px 20px;
		font-size: 14px;
	}
}

/* Animation classes */
.fade-in {
	animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.chart-loading {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 300px;
	background: #f8f9fa;
	border-radius: 8px;
	color: #6b7280;
	font-weight: 500;
}

.chart-loading::after {
	content: "";
	width: 20px;
	height: 20px;
	border: 2px solid #e5e7eb;
	border-top: 2px solid #3b82f6;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-left: 10px;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
