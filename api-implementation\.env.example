# FlahaSoil API Environment Configuration

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/flahasoil_db"

# For development with SQLite (easier setup)
# DATABASE_URL="file:./dev.db"

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:8000,http://127.0.0.1:5500

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Stripe Configuration (for payments)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Analytics
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID

# Logging
LOG_LEVEL=info
