# Changelog

All notable changes to the FlahaSoil project will be documented in this file.

## [2.0.0] - 2024-12-XX

### Major Cleanup & Restructure
- **BREAKING**: Removed excessive documentation files (16+ markdown files)
- **BREAKING**: Removed scattered test files from root directory
- **BREAKING**: Consolidated duplicate files and assets

### Added
- Proper project structure with organized directories
- Comprehensive API documentation (API.md)
- Enhanced package.json with proper scripts and metadata
- Dedicated tests/ directory for test files
- Clean README.md with updated installation instructions

### Removed
- Duplicate documentation files in Docs/ folder
- Scattered test files (test-*.js, test-*.html)
- Debug and development HTML files from public/
- Duplicate data files (data1.json)
- Duplicate soil calculation JavaScript files
- Empty logs/ and Docs/ directories

### Changed
- Streamlined public/ directory structure
- Improved project organization
- Updated README.md with current project structure
- Consolidated API documentation into single file

### Technical Improvements
- Better separation of frontend and backend code
- Cleaner asset organization
- Reduced project complexity
- Improved maintainability

## [1.x.x] - Previous Versions

### Features Implemented
- Interactive USDA Soil Triangle visualization
- Advanced soil property calculations using Saxton & Rawls (2006)
- User authentication and profile management
- Professional-grade soil analysis tools
- RESTful API for integration
- Tiered access system (Basic/Professional/Enterprise)
- Database integration with Prisma
- Email verification system
- Rate limiting and security features

---

**Note**: This changelog starts from version 2.0.0 after the major cleanup. Previous version history was not systematically tracked.
