/**
 * FlahaSoil Profile Page Styles
 *
 * @format
 */

/* Navigation Enhancements */
.navbar {
	background: var(--white);
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	position: sticky;
	top: 0;
	z-index: 1000;
}

.nav-link.active {
	color: var(--pa-accent);
	font-weight: 600;
}

.user-menu {
	position: relative;
}

.btn-user {
	background: var(--flaha-green);
	color: var(--white);
	border: none;
	padding: 10px 20px;
	border-radius: 6px;
	font-weight: 600;
	cursor: pointer;
	display: flex;
	align-items: center;
	gap: 8px;
	transition: all 0.3s ease;
}

.btn-user:hover {
	background: var(--tech-blue);
}

.dropdown-arrow {
	font-size: 0.8rem;
	transition: transform 0.3s ease;
}

.user-dropdown {
	position: absolute;
	top: 100%;
	right: 0;
	background: var(--white);
	border-radius: 8px;
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
	min-width: 180px;
	opacity: 0;
	visibility: hidden;
	transform: translateY(-10px);
	transition: all 0.3s ease;
	z-index: 1000;
}

.user-dropdown.show {
	opacity: 1;
	visibility: visible;
	transform: translateY(0);
}

.dropdown-item {
	display: block;
	padding: 12px 20px;
	color: var(--text-dark);
	text-decoration: none;
	transition: background 0.3s ease;
}

.dropdown-item:hover {
	background: var(--neutral-gray);
}

/* Mobile Navigation for Profile Page */
.mobile-nav-toggle {
	display: none;
	background: none;
	border: none;
	color: var(--text-dark);
	font-size: 24px;
	cursor: pointer;
	padding: 8px;
	border-radius: 6px;
	transition: background 0.3s ease;
}

.mobile-nav-toggle:hover {
	background: var(--light-gray);
}

.mobile-nav-toggle span {
	display: block;
	width: 25px;
	height: 3px;
	background: var(--text-dark);
	margin: 5px 0;
	transition: 0.3s;
	border-radius: 2px;
}

/* Profile Page Navbar Styling */
.navbar {
	background: linear-gradient(
		135deg,
		var(--flaha-green) 0%,
		var(--tech-blue) 100%
	);
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	padding: 15px 0;
}

.nav-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-brand .logo-link {
	display: flex;
	align-items: center;
	gap: 12px;
	text-decoration: none;
}

.nav-brand .nav-logo {
	height: 40px;
	width: auto;
}

.nav-brand .brand-text {
	display: flex;
	flex-direction: column;
	line-height: 1;
}

.nav-brand .flaha-text {
	font-family: "Montserrat", sans-serif;
	font-weight: 700;
	font-size: 18px;
	color: var(--white);
	letter-spacing: 1px;
}

.nav-brand .pa-text {
	font-family: "Montserrat", sans-serif;
	font-weight: 600;
	font-size: 14px;
	color: rgba(255, 255, 255, 0.9);
	letter-spacing: 0.5px;
}

.nav-brand .app-name {
	font-family: "Open Sans", sans-serif;
	font-weight: 600;
	font-size: 12px;
	color: rgba(255, 255, 255, 0.8);
	margin-top: 2px;
}

.nav-menu {
	display: flex;
	align-items: center;
	gap: 20px;
}

.nav-menu .nav-link {
	color: rgba(255, 255, 255, 0.9);
	text-decoration: none;
	font-weight: 500;
	padding: 10px 16px;
	border-radius: 8px;
	transition: all 0.3s ease;
	font-size: 14px;
}

.nav-menu .nav-link:hover {
	color: var(--white);
	background: rgba(255, 255, 255, 0.15);
}

.nav-menu .nav-link.active {
	color: var(--white);
	background: rgba(255, 255, 255, 0.2);
	font-weight: 600;
}

/* Mobile Navigation Responsive */
@media (max-width: 768px) {
	.nav-container {
		flex-wrap: nowrap;
		justify-content: space-between;
	}

	.mobile-nav-toggle {
		display: block;
	}

	.nav-menu {
		position: absolute;
		top: 100%;
		left: 0;
		right: 0;
		background: var(--white);
		flex-direction: column;
		gap: 0;
		padding: 20px;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		transform: translateY(-100%);
		opacity: 0;
		visibility: hidden;
		transition: all 0.3s ease;
		z-index: 999;
		border-top: 1px solid var(--neutral-gray);
	}

	.nav-menu.mobile-open {
		transform: translateY(0);
		opacity: 1;
		visibility: visible;
	}

	.nav-menu .nav-link {
		padding: 15px 20px;
		border-radius: 0;
		border-bottom: 1px solid var(--light-gray);
		text-align: center;
		width: 100%;
		display: block;
	}

	.nav-menu .nav-link:last-of-type {
		border-bottom: none;
		margin-bottom: 20px;
	}

	.user-menu {
		width: 100%;
		text-align: center;
		padding-top: 20px;
		border-top: 1px solid var(--light-gray);
	}

	.btn-user {
		width: auto;
		margin: 0 auto;
	}

	.user-dropdown {
		position: fixed;
		top: auto;
		bottom: 20px;
		left: 20px;
		right: 20px;
		min-width: auto;
	}
}

/* Main Content */
.main-content {
	padding: 40px 0;
	min-height: calc(100vh - 80px);
	background: var(--neutral-gray);
}

.container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

/* Profile Header */
.profile-header {
	background: var(--white);
	border-radius: 15px;
	padding: 40px;
	margin-bottom: 30px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
	gap: 20px;
}

.profile-info {
	display: flex;
	align-items: center;
	gap: 20px;
}

.profile-avatar {
	width: 80px;
	height: 80px;
	border-radius: 50%;
	background: linear-gradient(
		135deg,
		var(--flaha-green) 0%,
		var(--tech-blue) 100%
	);
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 2rem;
	font-weight: 700;
	color: var(--white);
	font-family: "Montserrat", sans-serif;
}

.profile-details h1 {
	font-family: "Montserrat", sans-serif;
	font-size: 2rem;
	font-weight: 700;
	color: var(--text-dark);
	margin-bottom: 5px;
}

.profile-details p {
	color: var(--text-light);
	margin-bottom: 15px;
	font-size: 1.1rem;
}

.user-tier {
	display: flex;
	align-items: center;
	gap: 10px;
}

.tier-badge {
	background: var(--pa-accent);
	color: var(--white);
	padding: 4px 12px;
	border-radius: 20px;
	font-size: 0.8rem;
	font-weight: 600;
	text-transform: uppercase;
}

.tier-badge.professional {
	background: var(--flaha-green);
}

.tier-badge.enterprise {
	background: var(--tech-blue);
}

.tier-description {
	color: var(--text-light);
	font-size: 0.9rem;
}

.profile-actions {
	display: flex;
	gap: 15px;
}

/* Dashboard Grid */
.dashboard-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
	gap: 30px;
}

.dashboard-card {
	background: var(--white);
	border-radius: 15px;
	padding: 30px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.dashboard-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 25px;
}

.card-header h3 {
	font-family: "Montserrat", sans-serif;
	font-size: 1.3rem;
	font-weight: 600;
	color: var(--text-dark);
}

.card-icon {
	font-size: 1.5rem;
}

/* Usage Statistics */
.usage-stats {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20px;
	margin-bottom: 25px;
}

.stat-item {
	text-align: center;
}

.stat-value {
	font-family: "Montserrat", sans-serif;
	font-size: 2rem;
	font-weight: 700;
	color: var(--pa-accent);
	margin-bottom: 5px;
}

.stat-label {
	color: var(--text-light);
	font-size: 0.9rem;
}

.usage-chart {
	margin-top: 20px;
}

.progress-bar {
	width: 100%;
	height: 8px;
	background: var(--neutral-gray);
	border-radius: 4px;
	overflow: hidden;
	margin-bottom: 10px;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(
		90deg,
		var(--flaha-green) 0%,
		var(--pa-accent) 100%
	);
	border-radius: 4px;
	transition: width 0.3s ease;
}

.usage-text {
	color: var(--text-light);
	font-size: 0.9rem;
	text-align: center;
}

/* Recent Analyses */
.recent-analyses {
	max-height: 300px;
	overflow-y: auto;
}

.analysis-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 0;
	border-bottom: 1px solid var(--neutral-gray);
}

.analysis-item:last-child {
	border-bottom: none;
}

.analysis-info h4 {
	font-weight: 600;
	color: var(--text-dark);
	margin-bottom: 5px;
}

.analysis-info p {
	color: var(--text-light);
	font-size: 0.9rem;
}

.analysis-date {
	color: var(--text-light);
	font-size: 0.8rem;
}

.loading {
	text-align: center;
	color: var(--text-light);
	padding: 40px 0;
}

/* Settings */
.settings-list {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.setting-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 0;
	border-bottom: 1px solid var(--neutral-gray);
}

.setting-item:last-child {
	border-bottom: none;
}

.setting-info h4 {
	font-weight: 600;
	color: var(--text-dark);
	margin-bottom: 5px;
}

.setting-info p {
	color: var(--text-light);
	font-size: 0.9rem;
}

/* Toggle Switch */
.toggle {
	position: relative;
	display: inline-block;
	width: 50px;
	height: 24px;
}

.toggle input {
	opacity: 0;
	width: 0;
	height: 0;
}

.slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	transition: 0.4s;
	border-radius: 24px;
}

.slider:before {
	position: absolute;
	content: "";
	height: 18px;
	width: 18px;
	left: 3px;
	bottom: 3px;
	background-color: white;
	transition: 0.4s;
	border-radius: 50%;
}

input:checked + .slider {
	background-color: var(--pa-accent);
}

input:checked + .slider:before {
	transform: translateX(26px);
}

/* Subscription Info */
.subscription-info {
	text-align: center;
}

.current-plan h4 {
	font-family: "Montserrat", sans-serif;
	font-size: 1.5rem;
	font-weight: 700;
	color: var(--text-dark);
	margin-bottom: 10px;
}

.current-plan p {
	color: var(--text-light);
	margin-bottom: 25px;
}

.plan-features {
	text-align: left;
	margin-bottom: 25px;
}

.feature {
	padding: 8px 0;
	color: var(--text-dark);
}

/* Email Verification Styles */
.email-container {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.verification-status {
	display: flex;
	align-items: center;
	gap: 10px;
	flex-wrap: wrap;
}

.verification-status.hidden {
	display: none;
}

.verification-badge {
	display: inline-flex;
	align-items: center;
	gap: 5px;
	padding: 4px 10px;
	border-radius: 12px;
	font-size: 0.8rem;
	font-weight: 500;
}

.verification-badge.verified {
	background: #d4edda;
	color: #155724;
	border: 1px solid #c3e6cb;
}

.verification-badge.unverified {
	background: #fff3cd;
	color: #856404;
	border: 1px solid #ffeaa7;
}

.verification-badge .icon {
	font-size: 0.9rem;
}

.btn-link {
	background: none;
	border: none;
	color: var(--pa-accent);
	text-decoration: underline;
	cursor: pointer;
	font-size: 0.85rem;
	padding: 0;
}

.btn-link:hover {
	color: var(--flaha-green);
}

/* Buttons */
.btn-outline {
	background: transparent;
	border: 2px solid var(--pa-accent);
	color: var(--pa-accent);
	padding: 8px 16px;
	border-radius: 6px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-outline:hover {
	background: var(--pa-accent);
	color: var(--white);
}

.full-width {
	width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
	.profile-header {
		flex-direction: column;
		text-align: center;
	}

	.profile-info {
		flex-direction: column;
		text-align: center;
	}

	.dashboard-grid {
		grid-template-columns: 1fr;
	}

	.usage-stats {
		grid-template-columns: 1fr;
		gap: 15px;
	}

	.setting-item {
		flex-direction: column;
		align-items: flex-start;
		gap: 15px;
	}
}
