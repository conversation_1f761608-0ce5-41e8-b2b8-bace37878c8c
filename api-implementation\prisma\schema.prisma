generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id             String         @id @default(cuid())
  email          String         @unique
  name           String
  password       String
  tier           String         @default("FREE")
  emailVerified  <PERSON>olean        @default(false)
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  planSelectedAt DateTime?
  usageCount     Int            @default(0)
  usageResetDate DateTime?
  soilAnalyses   SoilAnalysis[]
  subscription   Subscription?
  usageRecords   UsageRecord[]

  // Enhanced features
  comparativeAnalyses ComparativeAnalysis[]

  @@map("users")
}

model Subscription {
  id                   String   @id @default(cuid())
  userId               String   @unique
  tier                 String
  status               String   @default("ACTIVE")
  stripeCustomerId     String?
  stripeSubscriptionId String?
  stripePriceId        String?
  currentPeriodStart   DateTime
  currentPeriodEnd     DateTime
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  user                 User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model UsageRecord {
  id          String   @id @default(cuid())
  userId      String?
  endpoint    String
  ipAddress   String?
  userAgent   String?
  requestData String?
  timestamp   DateTime @default(now())
  user        User?    @relation(fields: [userId], references: [id])

  @@map("usage_records")
}

model SoilAnalysis {
  id                    String   @id @default(cuid())
  userId                String?
  sand                  Float
  clay                  Float
  silt                  Float
  organicMatter         Float    @default(2.5)
  densityFactor         Float    @default(1.0)
  fieldCapacity         Float
  wiltingPoint          Float
  plantAvailableWater   Float
  saturation            Float
  saturatedConductivity Float
  textureClass          String
  calculationSource     String
  ipAddress             String?
  createdAt             DateTime @default(now())
  user                  User?    @relation(fields: [userId], references: [id])

  // Enhanced features relationship
  enhancedAnalysis     EnhancedSoilAnalysis?

  @@index([userId, createdAt])
  @@index([createdAt])
  @@map("soil_analyses")
}

// NEW ENHANCED MODELS FOR ADVANCED FEATURES

// Regional soil characteristics database
model SoilRegion {
  id          String @id @default(cuid())
  
  // Geographic information
  regionName  String @unique // "US Midwest", "Mediterranean Basin", etc.
  country     String
  state       String?
  climateZone String // "Humid Continental", "Mediterranean", etc.
  
  // Regional environmental factors
  avgAnnualRainfall    Float  // mm
  avgAnnualTemperature Float  // °C
  frostFreeDays        Int?   // days per year
  
  // Typical soil characteristics for region
  typicalSandRange     String // "15-25%"
  typicalClayRange     String // "20-35%"
  typicalOMRange       String // "2-4%"
  
  // Regional adjustment factors (JSON stored as text)
  seasonalFactors      String? // JSON: seasonal adjustments
  climateAdjustments   String? // JSON: climate-based corrections
  
  // Administrative
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relationships
  enhancedAnalyses EnhancedSoilAnalysis[]
  
  @@map("soil_regions")
  @@index([country, state])
  @@index([climateZone])
}

// Enhanced soil analysis with regional data and visualization support
model EnhancedSoilAnalysis {
  id            String @id @default(cuid())
  
  // Link to base analysis
  baseAnalysisId String @unique
  baseAnalysis   SoilAnalysis @relation(fields: [baseAnalysisId], references: [id], onDelete: Cascade)
  
  // Regional context
  regionId      String?
  region        SoilRegion? @relation(fields: [regionId], references: [id], onDelete: SetNull)
  
  // Geographic location
  latitude      Float?
  longitude     Float?
  elevation     Float?  // meters above sea level
  siteDescription String? // "Field A, North Section"
  
  // Advanced soil parameters
  bulkDensity           Float?  // g/cm³
  particleDensity       Float?  // g/cm³
  totalPorosity         Float?  // %
  macroporosity         Float?  // %
  microporosity         Float?  // %
  
  // Chemical properties
  pH                    Float?
  electricalConductivity Float? // dS/m
  cationExchangeCapacity Float? // cmol/kg
  baseSaturation        Float?  // %
  
  // Physical properties  
  gravelContent         Float?  // %
  aggregateStability    Float?  // %
  infiltrationRate      Float?  // mm/hr
  
  // Calculated advanced results (stored as JSON text for flexibility)
  moistureTensionCurve  String? // JSON: tension-moisture data points
  profileData3D         String? // JSON: 3D soil profile visualization data
  seasonalVariation     String? // JSON: seasonal adjustment factors
  soilHorizons          String? // JSON: horizon data for 3D profile
  waterMovementModel    String? // JSON: water movement characteristics
  
  // Visualization metadata
  hasVisualizationData  Boolean @default(false)
  lastVisualizationCalc DateTime?
  
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relationships
  moistureTensionPoints MoistureTensionPoint[]
  comparativeAnalyses   ComparativeAnalysis[]
  managementRecommendations SoilManagementRecommendation[]
  
  @@map("enhanced_soil_analyses")
  @@index([regionId])
  @@index([latitude, longitude])
}

// Individual moisture-tension curve data points
model MoistureTensionPoint {
  id                    String @id @default(cuid())
  
  enhancedAnalysisId    String
  enhancedAnalysis      EnhancedSoilAnalysis @relation(fields: [enhancedAnalysisId], references: [id], onDelete: Cascade)
  
  // Data point
  tension               Float  // kPa (tension/suction)
  moistureContent       Float  // % by volume
  
  // Metadata
  isCalculated          Boolean @default(true) // vs measured
  calculationMethod     String? // "Saxton-Rawls", "Van Genuchten", etc.
  
  @@map("moisture_tension_points")
  @@index([enhancedAnalysisId, tension])
}

// Comparative analysis support
model ComparativeAnalysis {
  id          String @id @default(cuid())
  
  // Analysis metadata
  name        String // "Field Comparison 2025"
  description String?
  analysisType String // "temporal", "spatial", "treatment"
  
  // User who created the comparison
  userId      String?
  user        User?   @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  // Analyses being compared (stored as JSON for flexibility)
  analysisIds String  // JSON array of analysis IDs
  
  // Comparison results
  comparisonResults String? // JSON: statistical comparison results
  visualizationData String? // JSON: data for comparative charts
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Many-to-many relationship with enhanced analyses
  enhancedAnalyses EnhancedSoilAnalysis[]
  
  @@map("comparative_analyses")
  @@index([userId, createdAt])
}

// Soil management recommendations based on enhanced analysis
model SoilManagementRecommendation {
  id                    String @id @default(cuid())
  
  enhancedAnalysisId    String
  enhancedAnalysis      EnhancedSoilAnalysis @relation(fields: [enhancedAnalysisId], references: [id], onDelete: Cascade)
  
  // Recommendation details
  category              String // "irrigation", "fertilization", "tillage", "drainage"
  priority              String // "high", "medium", "low"
  title                 String
  description           String
  
  // Implementation details
  seasonalTiming        String? // "spring", "fall", etc.
  expectedImprovement   String? // "20% increase in PAW"
  costEstimate          String? // "low", "medium", "high"
  
  // Supporting data
  scientificBasis       String? // Reference to research
  localRelevance        Float?  // 0-1 relevance score for region
  
  isActive              Boolean @default(true)
  createdAt             DateTime @default(now())
  
  @@map("soil_management_recommendations")
  @@index([enhancedAnalysisId, priority])
  @@index([category])
}
